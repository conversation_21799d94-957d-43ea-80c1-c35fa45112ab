"""
Content Moderator Agent using Groq Whisper and Open Source AI Models
FutureSmart AI Challenge Entry
"""

import os
import cv2
import numpy as np
import torch
from groq import Groq
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import tempfile
from pathlib import Path
import json
import time

# Configure logging
logger = logging.getLogger(__name__)

class ModerationSeverity(Enum):
    """Severity levels for content moderation"""
    SAFE = "safe"
    WARNING = "warning"
    MODERATE = "moderate"
    SEVERE = "severe"
    CRITICAL = "critical"

@dataclass
class ModerationResult:
    """Result of content moderation analysis"""
    timestamp: float
    severity: ModerationSeverity
    category: str
    confidence: float
    description: str
    action_required: str
    frame_number: Optional[int] = None

class ContentModeratorAgent:
    """
    AI-powered content moderation agent using Groq Whisper and open-source models
    """
    
    def __init__(self):
        """Initialize the content moderator with AI models"""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Using device: {self.device}")
        
        # Initialize Groq client
        self.groq_client = Groq(api_key=os.getenv('GROQ_API_KEY'))
        
        # Load models
        self._load_models()
        
        # Moderation categories and thresholds
        self.moderation_categories = {
            "violence": {"threshold": float(os.getenv('VIOLENCE_THRESHOLD', 0.7)), "severity": ModerationSeverity.SEVERE},
            "nsfw": {"threshold": float(os.getenv('NSFW_THRESHOLD', 0.8)), "severity": ModerationSeverity.CRITICAL},
            "hate_speech": {"threshold": float(os.getenv('HATE_SPEECH_THRESHOLD', 0.6)), "severity": ModerationSeverity.MODERATE},
            "profanity": {"threshold": float(os.getenv('PROFANITY_THRESHOLD', 0.5)), "severity": ModerationSeverity.WARNING},
            "copyright": {"threshold": float(os.getenv('COPYRIGHT_THRESHOLD', 0.9)), "severity": ModerationSeverity.SEVERE}
        }
        
        # Profanity word list (basic implementation)
        self.profanity_words = [
            "fuck", "shit", "damn", "bitch", "asshole", "bastard", "crap", "hell",
            "piss", "slut", "whore", "fag", "nigger", "retard", "gay", "lesbian"
        ]
    
    def _load_models(self):
        """Load all required AI models"""
        try:
            # Load NSFW detection model
            logger.info("Loading NSFW detection model...")
            from transformers import pipeline
            
            self.nsfw_classifier = pipeline(
                "image-classification",
                model="Falconsai/nsfw_image_detection",
                device=0 if torch.cuda.is_available() else -1
            )
            
            # Load violence detection model (using a general image classifier)
            logger.info("Loading violence detection model...")
            self.violence_classifier = pipeline(
                "image-classification",
                model="google/vit-base-patch16-224",
                device=0 if torch.cuda.is_available() else -1
            )
            
            # Load text toxicity classifier
            logger.info("Loading text toxicity classifier...")
            self.toxicity_classifier = pipeline(
                "text-classification",
                model="unitary/toxic-bert",
                device=0 if torch.cuda.is_available() else -1
            )
            
            logger.info("All models loaded successfully!")
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            # Initialize with None to handle gracefully
            self.nsfw_classifier = None
            self.violence_classifier = None
            self.toxicity_classifier = None
    
    def get_models_status(self) -> Dict[str, bool]:
        """Get status of loaded models"""
        return {
            "groq_whisper": self.groq_client is not None,
            "nsfw_classifier": self.nsfw_classifier is not None,
            "violence_classifier": self.violence_classifier is not None,
            "toxicity_classifier": self.toxicity_classifier is not None
        }
    
    def extract_audio_from_video(self, video_path: str) -> str:
        """Extract audio from video file"""
        try:
            import moviepy.editor as mp
            
            # Create temporary audio file
            temp_audio = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
            temp_audio_path = temp_audio.name
            temp_audio.close()
            
            # Extract audio
            video = mp.VideoFileClip(video_path)
            audio = video.audio
            audio.write_audiofile(temp_audio_path, verbose=False, logger=None)
            
            video.close()
            audio.close()
            
            return temp_audio_path
            
        except Exception as e:
            logger.error(f"Error extracting audio: {e}")
            return None
    
    def transcribe_audio_with_groq(self, audio_path: str) -> Dict[str, Any]:
        """Transcribe audio using Groq Whisper"""
        try:
            with open(audio_path, "rb") as file:
                transcription = self.groq_client.audio.transcriptions.create(
                    file=(audio_path, file.read()),
                    model="whisper-large-v3",
                    response_format="verbose_json",
                    temperature=0.0
                )
            
            return {
                "text": transcription.text,
                "segments": getattr(transcription, 'segments', []),
                "language": getattr(transcription, 'language', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Error transcribing audio with Groq: {e}")
            return {"text": "", "segments": [], "language": "unknown"}
    
    def analyze_video_frame(self, frame: np.ndarray, timestamp: float, frame_number: int) -> List[ModerationResult]:
        """Analyze a single video frame for inappropriate content"""
        results = []
        
        try:
            # Convert frame to PIL Image
            from PIL import Image
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            
            # NSFW Detection
            if self.nsfw_classifier:
                try:
                    nsfw_result = self.nsfw_classifier(pil_image)
                    for prediction in nsfw_result:
                        if prediction['label'].lower() in ['nsfw', 'porn', 'explicit'] and \
                           prediction['score'] > self.moderation_categories['nsfw']['threshold']:
                            results.append(ModerationResult(
                                timestamp=timestamp,
                                severity=ModerationSeverity.CRITICAL,
                                category="nsfw",
                                confidence=prediction['score'],
                                description=f"NSFW content detected with {prediction['score']:.2%} confidence",
                                action_required="Immediate removal required",
                                frame_number=frame_number
                            ))
                except Exception as e:
                    logger.warning(f"NSFW detection failed for frame {frame_number}: {e}")
            
            # Violence Detection (simplified approach)
            if self.violence_classifier:
                try:
                    violence_keywords = ["weapon", "gun", "knife", "blood", "fight", "violence", "war"]
                    violence_result = self.violence_classifier(pil_image)
                    
                    for prediction in violence_result:
                        if any(keyword in prediction['label'].lower() for keyword in violence_keywords) and \
                           prediction['score'] > self.moderation_categories['violence']['threshold']:
                            results.append(ModerationResult(
                                timestamp=timestamp,
                                severity=ModerationSeverity.SEVERE,
                                category="violence",
                                confidence=prediction['score'],
                                description=f"Potential violent content detected: {prediction['label']}",
                                action_required="Review and potentially remove",
                                frame_number=frame_number
                            ))
                except Exception as e:
                    logger.warning(f"Violence detection failed for frame {frame_number}: {e}")
                    
        except Exception as e:
            logger.error(f"Error analyzing frame {frame_number} at {timestamp}s: {e}")
        
        return results
    
    def analyze_transcript(self, transcript_data: Dict[str, Any]) -> List[ModerationResult]:
        """Analyze transcript for inappropriate content"""
        results = []
        
        try:
            text = transcript_data.get("text", "")
            segments = transcript_data.get("segments", [])
            
            if not text.strip():
                return results
            
            # Analyze full text for toxicity
            if self.toxicity_classifier:
                try:
                    toxicity_result = self.toxicity_classifier(text)
                    
                    for prediction in toxicity_result:
                        if prediction['label'] == 'TOXIC' and \
                           prediction['score'] > self.moderation_categories['hate_speech']['threshold']:
                            results.append(ModerationResult(
                                timestamp=0.0,
                                severity=ModerationSeverity.MODERATE,
                                category="hate_speech",
                                confidence=prediction['score'],
                                description=f"Toxic language detected in transcript",
                                action_required="Review transcript and consider content warning"
                            ))
                except Exception as e:
                    logger.warning(f"Toxicity analysis failed: {e}")
            
            # Check for profanity
            text_lower = text.lower()
            for word in self.profanity_words:
                if word in text_lower:
                    # Find approximate timestamp if segments are available
                    timestamp = 0.0
                    if segments:
                        for segment in segments:
                            if word in segment.get('text', '').lower():
                                timestamp = segment.get('start', 0.0)
                                break
                    
                    results.append(ModerationResult(
                        timestamp=timestamp,
                        severity=ModerationSeverity.WARNING,
                        category="profanity",
                        confidence=0.9,
                        description=f"Profanity detected: '{word}' in transcript",
                        action_required="Consider age rating or content warning"
                    ))
                    
        except Exception as e:
            logger.error(f"Error analyzing transcript: {e}")
        
        return results
    
    def analyze_video(self, video_path: str, progress_callback: Optional[Callable] = None) -> List[ModerationResult]:
        """Analyze entire video for content moderation"""
        results = []
        
        try:
            # Open video
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError(f"Could not open video file: {video_path}")
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps if fps > 0 else 0
            
            logger.info(f"Analyzing video: {total_frames} frames, {duration:.2f}s duration, {fps:.2f} fps")
            
            # Analyze frames (sample every 2 seconds to reduce processing time)
            frame_interval = max(1, int(fps * 2))  # Sample every 2 seconds
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count % frame_interval == 0:
                    timestamp = frame_count / fps
                    frame_results = self.analyze_video_frame(frame, timestamp, frame_count)
                    results.extend(frame_results)
                    
                    # Update progress
                    if progress_callback:
                        progress = int((frame_count / total_frames) * 50)  # 50% for video analysis
                        progress_callback(progress)
                
                frame_count += 1
            
            cap.release()
            
            # Extract and analyze audio
            if progress_callback:
                progress_callback(60)
            
            audio_path = self.extract_audio_from_video(video_path)
            if audio_path:
                try:
                    # Transcribe with Groq Whisper
                    transcript_data = self.transcribe_audio_with_groq(audio_path)
                    
                    if progress_callback:
                        progress_callback(80)
                    
                    # Analyze transcript
                    transcript_results = self.analyze_transcript(transcript_data)
                    results.extend(transcript_results)
                    
                    # Clean up temporary audio file
                    os.unlink(audio_path)
                    
                except Exception as e:
                    logger.error(f"Error processing audio: {e}")
                finally:
                    if os.path.exists(audio_path):
                        os.unlink(audio_path)
            
            if progress_callback:
                progress_callback(100)
            
            logger.info(f"Video analysis completed. Found {len(results)} potential issues.")
            
        except Exception as e:
            logger.error(f"Error analyzing video: {e}")
            raise
        
        return results
    
    def generate_moderation_report(self, results: List[ModerationResult]) -> Dict[str, Any]:
        """Generate comprehensive moderation report"""
        if not results:
            return {
                "status": "clean",
                "total_issues": 0,
                "severity_breakdown": {},
                "recommendations": ["Content appears to be safe for all audiences"],
                "action_required": False,
                "risk_score": 0.0,
                "timestamp": time.time()
            }
        
        # Count issues by severity and category
        severity_counts = {}
        category_counts = {}
        
        for result in results:
            severity = result.severity.value
            category = result.category
            
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            category_counts[category] = category_counts.get(category, 0) + 1
        
        # Determine overall action required
        critical_issues = [r for r in results if r.severity == ModerationSeverity.CRITICAL]
        severe_issues = [r for r in results if r.severity == ModerationSeverity.SEVERE]
        
        action_required = len(critical_issues) > 0 or len(severe_issues) > 2
        
        # Generate recommendations
        recommendations = []
        if critical_issues:
            recommendations.append("🚨 IMMEDIATE ACTION: Remove or heavily edit content due to critical violations")
        if severe_issues:
            recommendations.append("⚠️ Review severe content violations and consider age restrictions")
        if category_counts.get("profanity", 0) > 0:
            recommendations.append("💬 Consider adding content warnings for language")
        if not action_required and results:
            recommendations.append("ℹ️ Minor issues detected - consider content warnings")
        
        # Calculate risk score
        risk_score = self._calculate_risk_score(results)
        
        return {
            "status": "flagged" if results else "clean",
            "total_issues": len(results),
            "severity_breakdown": severity_counts,
            "category_breakdown": category_counts,
            "detailed_results": [
                {
                    "timestamp": r.timestamp,
                    "frame_number": r.frame_number,
                    "severity": r.severity.value,
                    "category": r.category,
                    "confidence": round(r.confidence, 3),
                    "description": r.description,
                    "action": r.action_required
                }
                for r in results
            ],
            "recommendations": recommendations,
            "action_required": action_required,
            "risk_score": round(risk_score, 2),
            "timestamp": time.time()
        }
    
    def _calculate_risk_score(self, results: List[ModerationResult]) -> float:
        """Calculate overall risk score (0-100)"""
        if not results:
            return 0.0
        
        severity_weights = {
            ModerationSeverity.SAFE: 0,
            ModerationSeverity.WARNING: 10,
            ModerationSeverity.MODERATE: 25,
            ModerationSeverity.SEVERE: 60,
            ModerationSeverity.CRITICAL: 100
        }
        
        total_score = sum(severity_weights[result.severity] * result.confidence for result in results)
        max_possible_score = len(results) * 100
        
        return min(100.0, (total_score / max_possible_score) * 100) if max_possible_score > 0 else 0.0
