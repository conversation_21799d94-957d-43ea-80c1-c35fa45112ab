<template>
  <div class="upload-container">
    <div class="upload-card">
      <div class="upload-header">
        <h1>
          <el-icon class="header-icon"><Upload /></el-icon>
          Upload Video for Content Moderation
        </h1>
        <p>Select a video file to analyze for inappropriate content using AI</p>
      </div>

      <!-- Upload Area -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :action="uploadUrl"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-progress="handleUploadProgress"
          :show-file-list="false"
          accept="video/*"
          name="video"
        >
          <div class="upload-content">
            <el-icon class="upload-icon" v-if="!uploading && !uploadedFile">
              <VideoCamera />
            </el-icon>
            
            <div v-if="uploading" class="upload-progress">
              <el-icon class="is-loading"><Loading /></el-icon>
              <p>Uploading... {{ uploadProgress }}%</p>
              <el-progress :percentage="uploadProgress" :show-text="false" />
            </div>
            
            <div v-else-if="uploadedFile" class="upload-success">
              <el-icon class="success-icon"><CircleCheck /></el-icon>
              <p><strong>{{ uploadedFile.filename }}</strong></p>
              <p class="file-info">{{ uploadedFile.file_info?.size_mb }}MB • {{ uploadedFile.file_info?.video_properties?.duration_seconds }}s</p>
            </div>
            
            <div v-else class="upload-placeholder">
              <p class="upload-text">Drop video file here or <em>click to upload</em></p>
              <p class="upload-hint">Supports MP4, AVI, MOV, MKV, WebM (max 500MB)</p>
            </div>
          </div>
        </el-upload>
      </div>

      <!-- File Information -->
      <div v-if="uploadedFile" class="file-info-section">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>File Information</span>
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="Filename">
              {{ uploadedFile.filename }}
            </el-descriptions-item>
            <el-descriptions-item label="File Size">
              {{ uploadedFile.file_info?.size_mb }}MB
            </el-descriptions-item>
            <el-descriptions-item label="Duration">
              {{ formatDuration(uploadedFile.file_info?.video_properties?.duration_seconds) }}
            </el-descriptions-item>
            <el-descriptions-item label="Resolution">
              {{ uploadedFile.file_info?.video_properties?.resolution }}
            </el-descriptions-item>
            <el-descriptions-item label="Frame Rate">
              {{ uploadedFile.file_info?.video_properties?.fps?.toFixed(1) }} fps
            </el-descriptions-item>
            <el-descriptions-item label="Aspect Ratio">
              {{ uploadedFile.file_info?.video_properties?.aspect_ratio?.toFixed(2) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>

      <!-- Action Buttons -->
      <div class="action-section">
        <el-button 
          v-if="!uploadedFile" 
          type="primary" 
          size="large" 
          @click="triggerUpload"
          :disabled="uploading"
        >
          <el-icon><FolderOpened /></el-icon>
          Choose Video File
        </el-button>
        
        <div v-else class="action-buttons">
          <el-button 
            type="success" 
            size="large" 
            @click="startModeration"
            :loading="moderating"
            :disabled="moderating"
          >
            <el-icon><Play /></el-icon>
            {{ moderating ? 'Analyzing...' : 'Start Content Moderation' }}
          </el-button>
          
          <el-button 
            size="large" 
            @click="resetUpload"
            :disabled="moderating"
          >
            <el-icon><RefreshLeft /></el-icon>
            Upload Different Video
          </el-button>
        </div>
      </div>

      <!-- Moderation Progress -->
      <div v-if="moderating || moderationComplete" class="moderation-section">
        <el-card class="progress-card">
          <template #header>
            <div class="card-header">
              <el-icon class="is-loading" v-if="moderating"><Loading /></el-icon>
              <el-icon v-else><CircleCheck /></el-icon>
              <span>{{ moderating ? 'Content Analysis in Progress' : 'Analysis Complete' }}</span>
            </div>
          </template>
          
          <div class="progress-content">
            <el-progress 
              :percentage="moderationProgress" 
              :status="moderationComplete ? 'success' : undefined"
              :stroke-width="8"
            />
            <p class="progress-text">{{ moderationMessage }}</p>
            
            <div v-if="moderationComplete && moderationReport" class="quick-results">
              <el-tag 
                :type="getStatusType(moderationReport.status)" 
                size="large"
                class="status-tag"
              >
                {{ moderationReport.status.toUpperCase() }}
              </el-tag>
              
              <div class="result-stats">
                <div class="stat-item">
                  <span class="stat-label">Issues Found:</span>
                  <span class="stat-value">{{ moderationReport.total_issues }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Risk Score:</span>
                  <span class="stat-value">{{ moderationReport.risk_score }}/100</span>
                </div>
              </div>
              
              <el-button 
                type="primary" 
                @click="viewDetailedReport"
                class="view-report-btn"
              >
                <el-icon><Document /></el-icon>
                View Detailed Report
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import io from 'socket.io-client'

export default {
  name: 'Upload',
  setup() {
    const router = useRouter()
    const uploadRef = ref(null)
    
    // Upload state
    const uploading = ref(false)
    const uploadProgress = ref(0)
    const uploadedFile = ref(null)
    
    // Moderation state
    const moderating = ref(false)
    const moderationProgress = ref(0)
    const moderationMessage = ref('')
    const moderationComplete = ref(false)
    const moderationReport = ref(null)
    
    // Socket connection
    let socket = null
    
    const uploadUrl = `${import.meta.env.VITE_API_URL || 'http://localhost:5000'}/api/upload`
    
    const initSocket = () => {
      socket = io(import.meta.env.VITE_API_URL || 'http://localhost:5000')
      
      socket.on('connect', () => {
        console.log('Connected to server')
      })
      
      socket.on('moderation_progress', (data) => {
        if (data.file_id === uploadedFile.value?.file_id) {
          moderationProgress.value = data.progress
          moderationMessage.value = data.message
        }
      })
      
      socket.on('moderation_complete', (data) => {
        if (data.file_id === uploadedFile.value?.file_id) {
          moderating.value = false
          moderationComplete.value = true
          moderationProgress.value = 100
          moderationMessage.value = 'Analysis completed successfully!'
          moderationReport.value = data.report
          
          ElNotification({
            title: 'Analysis Complete',
            message: 'Content moderation has finished. Check the results below.',
            type: 'success',
            duration: 5000
          })
        }
      })
      
      socket.on('moderation_error', (data) => {
        if (data.file_id === uploadedFile.value?.file_id) {
          moderating.value = false
          moderationMessage.value = 'Analysis failed. Please try again.'
          
          ElMessage.error(`Moderation failed: ${data.error}`)
        }
      })
    }
    
    const beforeUpload = (file) => {
      const isVideo = file.type.startsWith('video/')
      const isLt500M = file.size / 1024 / 1024 < 500
      
      if (!isVideo) {
        ElMessage.error('Please upload a video file!')
        return false
      }
      if (!isLt500M) {
        ElMessage.error('Video file size cannot exceed 500MB!')
        return false
      }
      
      uploading.value = true
      uploadProgress.value = 0
      return true
    }
    
    const handleUploadProgress = (event) => {
      uploadProgress.value = Math.round((event.loaded / event.total) * 100)
    }
    
    const handleUploadSuccess = (response) => {
      uploading.value = false
      uploadedFile.value = response
      
      ElMessage.success('Video uploaded successfully!')
    }
    
    const handleUploadError = (error) => {
      uploading.value = false
      uploadProgress.value = 0
      
      ElMessage.error('Upload failed. Please try again.')
      console.error('Upload error:', error)
    }
    
    const triggerUpload = () => {
      uploadRef.value?.$el.querySelector('input').click()
    }
    
    const resetUpload = () => {
      uploadedFile.value = null
      moderating.value = false
      moderationProgress.value = 0
      moderationComplete.value = false
      moderationReport.value = null
      moderationMessage.value = ''
    }
    
    const startModeration = async () => {
      if (!uploadedFile.value) return
      
      moderating.value = true
      moderationProgress.value = 0
      moderationComplete.value = false
      moderationMessage.value = 'Initializing content analysis...'
      
      try {
        const response = await fetch(`${uploadUrl.replace('/upload', '')}/moderate/${uploadedFile.value.file_id}`, {
          method: 'POST'
        })
        
        if (!response.ok) {
          throw new Error('Failed to start moderation')
        }
        
        ElMessage.success('Content moderation started!')
        
      } catch (error) {
        moderating.value = false
        ElMessage.error('Failed to start moderation. Please try again.')
        console.error('Moderation error:', error)
      }
    }
    
    const viewDetailedReport = () => {
      router.push(`/analysis/${uploadedFile.value.file_id}`)
    }
    
    const formatDuration = (seconds) => {
      if (!seconds) return 'Unknown'
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins}:${secs.toString().padStart(2, '0')}`
    }
    
    const getStatusType = (status) => {
      switch (status) {
        case 'clean': return 'success'
        case 'flagged': return 'danger'
        default: return 'info'
      }
    }
    
    onMounted(() => {
      initSocket()
    })
    
    onUnmounted(() => {
      if (socket) {
        socket.disconnect()
      }
    })
    
    return {
      uploadRef,
      uploading,
      uploadProgress,
      uploadedFile,
      moderating,
      moderationProgress,
      moderationMessage,
      moderationComplete,
      moderationReport,
      uploadUrl,
      beforeUpload,
      handleUploadProgress,
      handleUploadSuccess,
      handleUploadError,
      triggerUpload,
      resetUpload,
      startModeration,
      viewDetailedReport,
      formatDuration,
      getStatusType
    }
  }
}
</script>

<style scoped>
.upload-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.upload-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.upload-header {
  text-align: center;
  margin-bottom: 40px;
}

.upload-header h1 {
  color: #2c3e50;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.header-icon {
  color: #667eea;
  font-size: 2rem;
}

.upload-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-dragger {
  width: 100%;
}

.upload-content {
  padding: 40px 20px;
  text-align: center;
}

.upload-icon {
  font-size: 4rem;
  color: #667eea;
  margin-bottom: 20px;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.upload-progress .el-icon {
  font-size: 3rem;
  color: #409eff;
}

.upload-success {
  color: #67c23a;
}

.success-icon {
  font-size: 3rem;
  color: #67c23a;
  margin-bottom: 15px;
}

.file-info {
  color: #909399;
  font-size: 0.9rem;
}

.upload-placeholder .upload-text {
  font-size: 1.2rem;
  color: #606266;
  margin-bottom: 10px;
}

.upload-hint {
  color: #909399;
  font-size: 0.9rem;
}

.file-info-section {
  margin-bottom: 30px;
}

.info-card {
  border-radius: 15px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
}

.action-section {
  text-align: center;
  margin-bottom: 30px;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.moderation-section {
  margin-top: 30px;
}

.progress-card {
  border-radius: 15px;
}

.progress-content {
  text-align: center;
}

.progress-text {
  margin: 15px 0;
  color: #606266;
}

.quick-results {
  margin-top: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
}

.status-tag {
  margin-bottom: 15px;
}

.result-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  color: #909399;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.view-report-btn {
  margin-top: 15px;
}

/* Responsive design */
@media (max-width: 768px) {
  .upload-card {
    padding: 20px;
  }
  
  .upload-header h1 {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .result-stats {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
