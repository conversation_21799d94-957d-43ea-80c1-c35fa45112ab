"""
Main Flask application for AI Content Moderator
FutureSmart AI Challenge Entry
"""

import os
import logging
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit
from werkzeug.utils import secure_filename
from dotenv import load_dotenv
import uuid
from pathlib import Path

# Import our custom modules
from agents.content_moderator_agent import ContentModeratorAgent
from utils.file_handler import FileHandler
from utils.video_processor import VideoProcessor
from config.settings import Config

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config.from_object(Config)

# Enable CORS
CORS(app, origins=os.getenv('CORS_ORIGINS', 'http://localhost:3000').split(','))

# Initialize SocketIO for real-time updates
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize our AI agent
try:
    content_moderator = ContentModeratorAgent()
    logger.info("Content Moderator Agent initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Content Moderator Agent: {e}")
    content_moderator = None

# Initialize utilities
file_handler = FileHandler()
video_processor = VideoProcessor()

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('logs', exist_ok=True)

@app.route('/')
def index():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "AI Content Moderator",
        "version": "1.0.0",
        "challenge": "FutureSmart AI"
    })

@app.route('/api/upload', methods=['POST'])
def upload_video():
    """Upload video file for content moderation"""
    try:
        if 'video' not in request.files:
            return jsonify({"error": "No video file provided"}), 400
        
        file = request.files['video']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        if not file_handler.allowed_file(file.filename):
            return jsonify({"error": "File type not allowed"}), 400
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_extension = filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{file_id}.{file_extension}"
        
        # Save file
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)
        
        # Get file info
        file_info = file_handler.get_file_info(file_path)
        
        logger.info(f"Video uploaded successfully: {unique_filename}")
        
        return jsonify({
            "success": True,
            "file_id": file_id,
            "filename": filename,
            "file_info": file_info,
            "message": "Video uploaded successfully"
        })
        
    except Exception as e:
        logger.error(f"Error uploading video: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/moderate/<file_id>', methods=['POST'])
def moderate_content(file_id):
    """Start content moderation for uploaded video"""
    try:
        if not content_moderator:
            return jsonify({"error": "Content moderator not available"}), 503
        
        # Find the video file
        video_path = None
        for ext in ['mp4', 'avi', 'mov', 'mkv', 'webm', 'm4v']:
            potential_path = os.path.join(app.config['UPLOAD_FOLDER'], f"{file_id}.{ext}")
            if os.path.exists(potential_path):
                video_path = potential_path
                break
        
        if not video_path:
            return jsonify({"error": "Video file not found"}), 404
        
        # Start moderation process (this will be done asynchronously)
        socketio.start_background_task(target=moderate_video_async, file_id=file_id, video_path=video_path)
        
        return jsonify({
            "success": True,
            "file_id": file_id,
            "status": "processing",
            "message": "Content moderation started"
        })
        
    except Exception as e:
        logger.error(f"Error starting moderation: {e}")
        return jsonify({"error": str(e)}), 500

def moderate_video_async(file_id, video_path):
    """Asynchronously moderate video content"""
    try:
        # Emit progress update
        socketio.emit('moderation_progress', {
            'file_id': file_id,
            'status': 'started',
            'progress': 0,
            'message': 'Starting content analysis...'
        })
        
        # Process video
        results = content_moderator.analyze_video(video_path, progress_callback=lambda p: socketio.emit('moderation_progress', {
            'file_id': file_id,
            'status': 'processing',
            'progress': p,
            'message': f'Analyzing content... {p}%'
        }))
        
        # Generate report
        report = content_moderator.generate_moderation_report(results)
        
        # Emit completion
        socketio.emit('moderation_complete', {
            'file_id': file_id,
            'status': 'completed',
            'progress': 100,
            'report': report
        })
        
        logger.info(f"Content moderation completed for {file_id}")
        
    except Exception as e:
        logger.error(f"Error during moderation: {e}")
        socketio.emit('moderation_error', {
            'file_id': file_id,
            'status': 'error',
            'error': str(e)
        })

@app.route('/api/report/<file_id>', methods=['GET'])
def get_moderation_report(file_id):
    """Get moderation report for a file"""
    try:
        # In a real application, you'd retrieve this from a database
        # For now, we'll return a placeholder
        return jsonify({
            "file_id": file_id,
            "status": "completed",
            "message": "Report retrieved successfully"
        })
        
    except Exception as e:
        logger.error(f"Error retrieving report: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/models/status', methods=['GET'])
def get_models_status():
    """Get status of AI models"""
    try:
        status = {
            "content_moderator": content_moderator is not None,
            "models_loaded": content_moderator.get_models_status() if content_moderator else {},
            "device": content_moderator.device.type if content_moderator else "unknown"
        }
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Error getting models status: {e}")
        return jsonify({"error": str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info("Client connected")
    emit('connected', {'message': 'Connected to Content Moderator'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info("Client disconnected")

@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    
    logger.info(f"Starting Content Moderator API on port {port}")
    socketio.run(app, host='0.0.0.0', port=port, debug=debug)
