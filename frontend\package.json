{"name": "content-moderator-frontend", "version": "1.0.0", "description": "Frontend for AI Content Moderator - FutureSmart AI Challenge", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 8080"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "axios": "^1.5.0", "socket.io-client": "^4.7.2", "element-plus": "^2.3.12", "@element-plus/icons-vue": "^2.1.0", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "video.js": "^8.5.2", "videojs-contrib-hls": "^5.15.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "sass": "^1.66.1"}, "keywords": ["vue", "content-moderation", "ai", "video-analysis", "futuresmart-ai"], "author": "FutureSmart AI Challenge Participant", "license": "MIT"}