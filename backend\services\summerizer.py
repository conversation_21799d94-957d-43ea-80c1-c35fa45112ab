from transformers import pipeline
from typing import List, <PERSON><PERSON>

# Use Hugging Face summarization pipeline
summarizer = pipeline("summarization", model="facebook/bart-large-cnn")

def chunk_text(text: str, max_words=400) -> List[str]:
    words = text.split()
    return [' '.join(words[i:i+max_words]) for i in range(0, len(words), max_words)]

def summarize_transcript(transcript: str) -> Tuple[str, List[dict]]:
    chunks = chunk_text(transcript)
    summaries = []

    for chunk in chunks:
        try:
            summary = summarizer(chunk, max_length=100, min_length=30, do_sample=False)[0]["summary_text"]
            summaries.append(summary)
        except Exception:
            summaries.append("⚠️ Failed to summarize chunk.")

    # Generate pseudo-chapters (optional): Every Nth summary = 1 chapter
    chapters = [
        {"chapter": f"Section {i+1}", "summary": summaries[i]}
        for i in range(len(summaries))
    ]

    full_summary = '\n\n'.join(summaries)
    return full_summary, chapters
