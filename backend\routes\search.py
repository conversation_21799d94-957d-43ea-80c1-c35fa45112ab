from fastapi import APIRouter, Query
from backend.services.semantic_search import build_index, semantic_search
from pathlib import Path
import json

router = APIRouter()

@router.get("/")
def search_transcript(filename: str = Query(...), query: str = Query(...)):
    transcript_path = Path("data/transcripts") / f"{Path(filename).stem}.json"

    if not transcript_path.exists():
        return {"error": "Transcript file not found"}

    with open(transcript_path, "r", encoding="utf-8") as f:
        transcript_data = json.load(f)
    
    transcript_text = transcript_data.get("transcript", "")
    index = build_index(transcript_text)
    matches = semantic_search(query, index)

    return {"results": matches}
