"""
File handling utilities for the Content Moderator
"""

import os
import mimetypes
import magic
from pathlib import Path
from typing import Dict, Any, Optional
import cv2
import logging

logger = logging.getLogger(__name__)

class FileHandler:
    """Handle file operations and validation"""
    
    def __init__(self):
        self.allowed_extensions = {
            'mp4', 'avi', 'mov', 'mkv', 'webm', 'm4v', 
            'flv', 'wmv', '3gp', 'ogv', 'mpg', 'mpeg'
        }
        self.max_file_size = 500 * 1024 * 1024  # 500MB
    
    def allowed_file(self, filename: str) -> bool:
        """Check if file extension is allowed"""
        if not filename:
            return False
        
        extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
        return extension in self.allowed_extensions
    
    def validate_file_size(self, file_path: str) -> bool:
        """Check if file size is within limits"""
        try:
            file_size = os.path.getsize(file_path)
            return file_size <= self.max_file_size
        except OSError:
            return False
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get comprehensive file information"""
        try:
            file_stats = os.stat(file_path)
            file_size = file_stats.st_size
            
            # Get MIME type
            mime_type, _ = mimetypes.guess_type(file_path)
            
            # Try to get more detailed file type using python-magic
            try:
                file_type = magic.from_file(file_path, mime=True)
            except:
                file_type = mime_type
            
            # Get video properties if it's a video file
            video_info = self._get_video_properties(file_path)
            
            return {
                "filename": os.path.basename(file_path),
                "size_bytes": file_size,
                "size_mb": round(file_size / (1024 * 1024), 2),
                "mime_type": mime_type,
                "file_type": file_type,
                "is_valid_size": file_size <= self.max_file_size,
                "video_properties": video_info
            }
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }
    
    def _get_video_properties(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Extract video properties using OpenCV"""
        try:
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                return None
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0
            
            cap.release()
            
            return {
                "duration_seconds": round(duration, 2),
                "fps": round(fps, 2),
                "frame_count": frame_count,
                "resolution": f"{width}x{height}",
                "width": width,
                "height": height,
                "aspect_ratio": round(width / height, 2) if height > 0 else 0
            }
            
        except Exception as e:
            logger.warning(f"Could not extract video properties: {e}")
            return None
    
    def cleanup_file(self, file_path: str) -> bool:
        """Safely delete a file"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Cleaned up file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error cleaning up file {file_path}: {e}")
            return False
    
    def create_thumbnail(self, video_path: str, output_path: str, timestamp: float = 1.0) -> bool:
        """Create a thumbnail from video at specified timestamp"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return False
            
            # Set position to timestamp
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_number = int(timestamp * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # Read frame
            ret, frame = cap.read()
            if ret:
                # Resize to thumbnail size (maintain aspect ratio)
                height, width = frame.shape[:2]
                max_size = 300
                
                if width > height:
                    new_width = max_size
                    new_height = int(height * (max_size / width))
                else:
                    new_height = max_size
                    new_width = int(width * (max_size / height))
                
                thumbnail = cv2.resize(frame, (new_width, new_height))
                
                # Save thumbnail
                cv2.imwrite(output_path, thumbnail)
                cap.release()
                return True
            
            cap.release()
            return False
            
        except Exception as e:
            logger.error(f"Error creating thumbnail: {e}")
            return False
