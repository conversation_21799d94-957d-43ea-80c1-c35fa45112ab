"""
Configuration settings for the Content Moderator application
"""

import os
from pathlib import Path

class Config:
    """Base configuration class"""
    
    # Flask Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    
    # File Upload Configuration
    MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500MB max file size
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')
    ALLOWED_EXTENSIONS = set(os.getenv('ALLOWED_EXTENSIONS', 'mp4,avi,mov,mkv,webm,m4v').split(','))
    
    # Database Configuration
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///content_moderator.db')
    
    # AI Model Configuration
    DEVICE = os.getenv('DEVICE', 'auto')
    MODEL_CACHE_DIR = os.getenv('MODEL_CACHE_DIR', './models')
    BATCH_SIZE = int(os.getenv('BATCH_SIZE', '8'))
    
    # Content Moderation Thresholds
    NSFW_THRESHOLD = float(os.getenv('NSFW_THRESHOLD', '0.8'))
    VIOLENCE_THRESHOLD = float(os.getenv('VIOLENCE_THRESHOLD', '0.7'))
    HATE_SPEECH_THRESHOLD = float(os.getenv('HATE_SPEECH_THRESHOLD', '0.6'))
    PROFANITY_THRESHOLD = float(os.getenv('PROFANITY_THRESHOLD', '0.5'))
    COPYRIGHT_THRESHOLD = float(os.getenv('COPYRIGHT_THRESHOLD', '0.9'))
    
    # API Keys
    GROQ_API_KEY = os.getenv('GROQ_API_KEY')
    VIDEODB_API_KEY = os.getenv('VIDEODB_API_KEY')
    HUGGINGFACE_API_KEY = os.getenv('HUGGINGFACE_API_KEY')
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
    
    # Slack Configuration (Optional)
    SLACK_BOT_TOKEN = os.getenv('SLACK_BOT_TOKEN')
    SLACK_CHANNEL = os.getenv('SLACK_CHANNEL', 'content-moderation-alerts')
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/content_moderator.log')
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(Config.MODEL_CACHE_DIR, exist_ok=True)
        os.makedirs(os.path.dirname(Config.LOG_FILE), exist_ok=True)

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    
class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    
class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DATABASE_URL = 'sqlite:///:memory:'

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
