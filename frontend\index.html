<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Content Moderator - FutureSmart AI Challenge</title>
    <meta name="description" content="AI-powered video content moderation using open source models and Groq Whisper" />
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="//unpkg.com/element-plus/dist/index.css" />
    
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.5.2/video-js.css" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }
      
      #app {
        min-height: 100vh;
      }
      
      .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        color: white;
        font-size: 18px;
      }
      
      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="loading-spinner">
        <div>Loading AI Content Moderator...</div>
      </div>
    </div>
    
    <!-- Video.js JavaScript -->
    <script src="https://vjs.zencdn.net/8.5.2/video.min.js"></script>
    
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
