"""
Video processing utilities for content analysis
"""

import cv2
import numpy as np
from typing import List, <PERSON><PERSON>, Generator, Optional
import logging
import tempfile
import os

logger = logging.getLogger(__name__)

class VideoProcessor:
    """Handle video processing operations"""
    
    def __init__(self):
        self.supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.m4v']
    
    def extract_frames(self, video_path: str, interval_seconds: float = 2.0) -> Generator[Tuple[np.ndarray, float, int], None, None]:
        """
        Extract frames from video at specified intervals
        
        Args:
            video_path: Path to video file
            interval_seconds: Interval between frames in seconds
            
        Yields:
            Tuple of (frame, timestamp, frame_number)
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError(f"Could not open video: {video_path}")
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0:
                fps = 30  # Default fallback
            
            frame_interval = max(1, int(fps * interval_seconds))
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count % frame_interval == 0:
                    timestamp = frame_count / fps
                    yield frame, timestamp, frame_count
                
                frame_count += 1
            
            cap.release()
            
        except Exception as e:
            logger.error(f"Error extracting frames: {e}")
            raise
    
    def get_video_info(self, video_path: str) -> dict:
        """Get detailed video information"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError(f"Could not open video: {video_path}")
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = frame_count / fps if fps > 0 else 0
            
            cap.release()
            
            return {
                'fps': fps,
                'frame_count': frame_count,
                'width': width,
                'height': height,
                'duration': duration,
                'resolution': f"{width}x{height}",
                'aspect_ratio': width / height if height > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting video info: {e}")
            return {}
    
    def extract_audio_segment(self, video_path: str, start_time: float = 0, duration: Optional[float] = None) -> str:
        """
        Extract audio segment from video
        
        Args:
            video_path: Path to video file
            start_time: Start time in seconds
            duration: Duration in seconds (None for entire audio)
            
        Returns:
            Path to extracted audio file
        """
        try:
            import moviepy.editor as mp
            
            # Create temporary audio file
            temp_audio = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
            temp_audio_path = temp_audio.name
            temp_audio.close()
            
            # Load video and extract audio
            video = mp.VideoFileClip(video_path)
            
            if duration:
                audio = video.subclip(start_time, start_time + duration).audio
            else:
                audio = video.subclip(start_time).audio
            
            # Write audio file
            audio.write_audiofile(temp_audio_path, verbose=False, logger=None)
            
            # Clean up
            video.close()
            audio.close()
            
            return temp_audio_path
            
        except Exception as e:
            logger.error(f"Error extracting audio segment: {e}")
            return None
    
    def create_video_preview(self, video_path: str, output_path: str, max_duration: float = 30.0) -> bool:
        """
        Create a preview/trailer of the video
        
        Args:
            video_path: Input video path
            output_path: Output preview path
            max_duration: Maximum duration of preview in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            import moviepy.editor as mp
            
            video = mp.VideoFileClip(video_path)
            video_duration = video.duration
            
            if video_duration <= max_duration:
                # If video is short, just copy it
                preview = video
            else:
                # Create preview from multiple segments
                segment_duration = min(5.0, max_duration / 4)  # 4 segments of 5 seconds each
                segments = []
                
                for i in range(4):
                    start_time = (video_duration / 4) * i
                    end_time = min(start_time + segment_duration, video_duration)
                    segment = video.subclip(start_time, end_time)
                    segments.append(segment)
                
                preview = mp.concatenate_videoclips(segments)
            
            # Write preview
            preview.write_videofile(output_path, verbose=False, logger=None)
            
            # Clean up
            video.close()
            preview.close()
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating video preview: {e}")
            return False
    
    def blur_regions(self, video_path: str, output_path: str, blur_regions: List[dict]) -> bool:
        """
        Blur specific regions in video (for content moderation)
        
        Args:
            video_path: Input video path
            output_path: Output video path
            blur_regions: List of regions to blur with format:
                         [{'start_time': float, 'end_time': float, 'x': int, 'y': int, 'width': int, 'height': int}]
        
        Returns:
            True if successful, False otherwise
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return False
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # Create video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                timestamp = frame_count / fps
                
                # Apply blur to regions that are active at this timestamp
                for region in blur_regions:
                    if region['start_time'] <= timestamp <= region['end_time']:
                        x, y, w, h = region['x'], region['y'], region['width'], region['height']
                        
                        # Ensure coordinates are within frame bounds
                        x = max(0, min(x, width - 1))
                        y = max(0, min(y, height - 1))
                        w = min(w, width - x)
                        h = min(h, height - y)
                        
                        # Extract region and apply blur
                        roi = frame[y:y+h, x:x+w]
                        blurred_roi = cv2.GaussianBlur(roi, (51, 51), 0)
                        frame[y:y+h, x:x+w] = blurred_roi
                
                out.write(frame)
                frame_count += 1
            
            cap.release()
            out.release()
            
            return True
            
        except Exception as e:
            logger.error(f"Error blurring video regions: {e}")
            return False
    
    def extract_keyframes(self, video_path: str, num_frames: int = 10) -> List[Tuple[np.ndarray, float]]:
        """
        Extract key frames from video for analysis
        
        Args:
            video_path: Path to video file
            num_frames: Number of key frames to extract
            
        Returns:
            List of (frame, timestamp) tuples
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return []
            
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # Calculate frame indices to extract
            if total_frames <= num_frames:
                frame_indices = list(range(total_frames))
            else:
                step = total_frames // num_frames
                frame_indices = [i * step for i in range(num_frames)]
            
            keyframes = []
            
            for frame_idx in frame_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                
                if ret:
                    timestamp = frame_idx / fps
                    keyframes.append((frame, timestamp))
            
            cap.release()
            return keyframes
            
        except Exception as e:
            logger.error(f"Error extracting keyframes: {e}")
            return []
