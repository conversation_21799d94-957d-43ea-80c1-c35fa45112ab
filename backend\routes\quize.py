from fastapi import APIRouter, Query
from backend.services.quiz_gen import generate_quiz

router = APIRouter()

@router.get("/")
def quiz_from_summary(
    text: str = Query(..., description="Summarized content to generate quiz from"),
    difficulty: str = Query("medium", enum=["easy", "medium", "hard"])
):
    try:
        quiz = generate_quiz(text, difficulty)
        return {"quiz": quiz}
    except Exception as e:
        return {"error": str(e)}
