from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from backend.routes import upload, summarize, quiz, search, concepts

app = FastAPI(
    title="Smart Educational Video Assistant",
    description="Automatically summarizes, quizzes, and searches educational videos.",
    version="1.0.0"
)

# CORS (so Streamlit frontend can call API)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Update with frontend domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Register API routes
app.include_router(upload.router, prefix="/upload", tags=["Upload"])
app.include_router(summarize.router, prefix="/summarize", tags=["Summarize"])
app.include_router(quiz.router, prefix="/quiz", tags=["Quiz"])
app.include_router(search.router, prefix="/search", tags=["Search"])
app.include_router(concepts.router, prefix="/concepts", tags=["Concepts"])

@app.get("/")
def root():
    return {"message": "Smart Educational Video Assistant API is running"}
