from fastapi import APIRouter, Query
from backend.services.transcription import transcribe_audio_groq, save_transcript
from backend.services.summarizer import summarize_transcript
from pathlib import Path
import os

router = APIRouter()

@router.get("/")
def summarize_video(filename: str = Query(..., description="Uploaded video file name")):
    video_path = Path("data/uploads") / filename
    if not video_path.exists():
        return {"error": "File not found"}

    # Extract audio (optional step – assumes video is .mp3 or audio-only already)
    audio_path = video_path  # For now, assume it's already audio

    # Step 1: Transcribe
    try:
        transcript = transcribe_audio_groq(str(audio_path))
    except Exception as e:
        return {"error": str(e)}

    # Step 2: Save transcript
    transcript_path = save_transcript(transcript, filename)

    # Step 3: Summarize transcript
    summary, chapters = summarize_transcript(transcript)

    return {
        "summary": summary,
        "chapters": chapters,
        "transcript_path": transcript_path
    }
