import os
import requests
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

GROQ_API_KEY = os.getenv("GROQ_API_KEY")
GROQ_API_URL = "https://api.groq.com/openai/v1/audio/transcriptions"

def transcribe_audio_groq(audio_path: str) -> str:
    headers = {
        "Authorization": f"Bearer {GROQ_API_KEY}"
    }
    with open(audio_path, "rb") as audio_file:
        files = {
            "file": audio_file,
            "model": (None, "whisper-large-v3")
        }
        response = requests.post(GROQ_API_URL, headers=headers, files=files)

    if response.status_code == 200:
        return response.json()["text"]
    else:
        raise Exception(f"Transcription failed: {response.text}")
