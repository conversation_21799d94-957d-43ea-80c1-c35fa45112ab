import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'

import App from './App.vue'
import Home from './views/Home.vue'
import Upload from './views/Upload.vue'
import Analysis from './views/Analysis.vue'
import Reports from './views/Reports.vue'

// Create router
const routes = [
  { path: '/', name: 'Home', component: Home },
  { path: '/upload', name: 'Upload', component: Upload },
  { path: '/analysis/:fileId?', name: 'Analysis', component: Analysis, props: true },
  { path: '/reports', name: 'Reports', component: Reports }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Create app
const app = createApp(App)

// Register Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// Use plugins
app.use(router)
app.use(ElementPlus)

// Global properties
app.config.globalProperties.$apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5000'

// Mount app
app.mount('#app')
