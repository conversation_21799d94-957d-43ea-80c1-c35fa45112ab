<template>
  <div id="app">
    <!-- Navigation Header -->
    <el-header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <el-icon class="logo-icon" size="32"><VideoCamera /></el-icon>
          <h1 class="app-title">AI Content Moderator</h1>
          <el-tag type="success" size="small">FutureSmart AI Challenge</el-tag>
        </div>
        
        <el-menu
          :default-active="$route.path"
          mode="horizontal"
          router
          class="nav-menu"
          background-color="transparent"
          text-color="#fff"
          active-text-color="#ffd04b"
        >
          <el-menu-item index="/">
            <el-icon><House /></el-icon>
            <span>Home</span>
          </el-menu-item>
          <el-menu-item index="/upload">
            <el-icon><Upload /></el-icon>
            <span>Upload Video</span>
          </el-menu-item>
          <el-menu-item index="/analysis">
            <el-icon><DataAnalysis /></el-icon>
            <span>Analysis</span>
          </el-menu-item>
          <el-menu-item index="/reports">
            <el-icon><Document /></el-icon>
            <span>Reports</span>
          </el-menu-item>
        </el-menu>
        
        <div class="header-actions">
          <el-button @click="checkModelsStatus" type="primary" size="small">
            <el-icon><Refresh /></el-icon>
            Models Status
          </el-button>
        </div>
      </div>
    </el-header>

    <!-- Main Content -->
    <el-main class="app-main">
      <router-view />
    </el-main>

    <!-- Footer -->
    <el-footer class="app-footer">
      <div class="footer-content">
        <p>&copy; 2024 AI Content Moderator - Built for FutureSmart AI Challenge</p>
        <p>Powered by Groq Whisper, Open Source AI Models & VideoDB</p>
      </div>
    </el-footer>

    <!-- Models Status Dialog -->
    <el-dialog
      v-model="modelsDialogVisible"
      title="AI Models Status"
      width="500px"
    >
      <div v-if="modelsStatus">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="Groq Whisper">
            <el-tag :type="modelsStatus.groq_whisper ? 'success' : 'danger'">
              {{ modelsStatus.groq_whisper ? 'Ready' : 'Not Available' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="NSFW Classifier">
            <el-tag :type="modelsStatus.nsfw_classifier ? 'success' : 'danger'">
              {{ modelsStatus.nsfw_classifier ? 'Loaded' : 'Not Loaded' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Violence Classifier">
            <el-tag :type="modelsStatus.violence_classifier ? 'success' : 'danger'">
              {{ modelsStatus.violence_classifier ? 'Loaded' : 'Not Loaded' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Toxicity Classifier">
            <el-tag :type="modelsStatus.toxicity_classifier ? 'success' : 'danger'">
              {{ modelsStatus.toxicity_classifier ? 'Loaded' : 'Not Loaded' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-else class="loading-status">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>Checking models status...</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

export default {
  name: 'App',
  setup() {
    const modelsDialogVisible = ref(false)
    const modelsStatus = ref(null)

    const checkModelsStatus = async () => {
      modelsDialogVisible.value = true
      modelsStatus.value = null
      
      try {
        const response = await axios.get('/api/models/status')
        modelsStatus.value = response.data
      } catch (error) {
        console.error('Error checking models status:', error)
        ElMessage.error('Failed to check models status')
        modelsStatus.value = {
          groq_whisper: false,
          nsfw_classifier: false,
          violence_classifier: false,
          toxicity_classifier: false
        }
      }
    }

    onMounted(() => {
      // Check models status on app load
      checkModelsStatus()
    })

    return {
      modelsDialogVisible,
      modelsStatus,
      checkModelsStatus
    }
  }
}
</script>

<style scoped>
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  height: 70px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  color: #ffd04b;
}

.app-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.nav-menu {
  flex: 1;
  justify-content: center;
  border: none;
}

.nav-menu .el-menu-item {
  border: none;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
}

.nav-menu .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffd04b;
}

.nav-menu .el-menu-item.is-active {
  background-color: rgba(255, 255, 255, 0.15);
  color: #ffd04b;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-main {
  min-height: calc(100vh - 140px);
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.app-footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 20px;
  height: auto;
}

.footer-content p {
  margin: 5px 0;
  font-size: 14px;
}

.loading-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }
  
  .nav-menu {
    width: 100%;
    justify-content: space-around;
  }
  
  .app-title {
    font-size: 20px;
  }
}
</style>
