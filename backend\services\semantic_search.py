from sentence_transformers import SentenceTransformer, util
from typing import List, Dict
import json
from pathlib import Path

# Load pre-trained sentence embedding model
model = SentenceTransformer("all-MiniLM-L6-v2")

def chunk_transcript(transcript: str, max_words=100) -> List[str]:
    words = transcript.split()
    return [' '.join(words[i:i+max_words]) for i in range(0, len(words), max_words)]

def build_index(transcript_text: str) -> Dict:
    chunks = chunk_transcript(transcript_text)
    embeddings = model.encode(chunks, convert_to_tensor=True)
    return {"chunks": chunks, "embeddings": embeddings}

def semantic_search(query: str, index: Dict, top_k: int = 5) -> List[Dict]:
    query_embedding = model.encode(query, convert_to_tensor=True)
    scores = util.pytorch_cos_sim(query_embedding, index["embeddings"])[0]
    top_results = scores.topk(top_k)

    results = []
    for score, idx in zip(top_results.values, top_results.indices):
        results.append({
            "score": float(score),
            "text": index["chunks"][idx]
        })
    return results
