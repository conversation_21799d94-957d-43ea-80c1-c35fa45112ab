#!/usr/bin/env python3
"""
Setup script for AI Content Moderator - FutureSmart AI Challenge
"""

import os
import subprocess
import sys
import platform
from pathlib import Path

def print_banner():
    """Print setup banner"""
    print("=" * 60)
    print("🚀 AI Content Moderator Setup")
    print("   FutureSmart AI Challenge Entry")
    print("=" * 60)
    print()

def check_python_version():
    """Check Python version compatibility"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("   Please install Python 3.8 or higher")
        return False

def check_node_version():
    """Check Node.js version"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js {version} found")
            return True
        else:
            print("❌ Node.js not found")
            return False
    except FileNotFoundError:
        print("❌ Node.js not found")
        print("   Please install Node.js 16+ from https://nodejs.org/")
        return False

def run_command(command, description, cwd=None):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            cwd=cwd
        )
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during {description}")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def setup_backend():
    """Setup backend environment"""
    print("\n📦 Setting up Backend...")
    
    # Create virtual environment if it doesn't exist
    if not os.path.exists("backend/venv"):
        if not run_command(
            f"{sys.executable} -m venv backend/venv",
            "Creating Python virtual environment"
        ):
            return False
    
    # Determine activation script based on OS
    if platform.system() == "Windows":
        activate_script = "backend/venv/Scripts/activate"
        pip_path = "backend/venv/Scripts/pip"
    else:
        activate_script = "backend/venv/bin/activate"
        pip_path = "backend/venv/bin/pip"
    
    # Install requirements
    if not run_command(
        f"{pip_path} install -r backend/requirements.txt",
        "Installing Python dependencies"
    ):
        return False
    
    # Create necessary directories
    os.makedirs("backend/uploads", exist_ok=True)
    os.makedirs("backend/logs", exist_ok=True)
    os.makedirs("backend/models", exist_ok=True)
    
    print("✅ Backend setup completed")
    return True

def setup_frontend():
    """Setup frontend environment"""
    print("\n🎨 Setting up Frontend...")
    
    # Install npm dependencies
    if not run_command(
        "npm install",
        "Installing Node.js dependencies",
        cwd="frontend"
    ):
        return False
    
    print("✅ Frontend setup completed")
    return True

def create_env_files():
    """Create environment files from templates"""
    print("\n⚙️ Setting up environment files...")
    
    # Backend .env
    backend_env_template = "backend/.env.template"
    backend_env = "backend/.env"
    
    if os.path.exists(backend_env_template) and not os.path.exists(backend_env):
        import shutil
        shutil.copy(backend_env_template, backend_env)
        print(f"✅ Created {backend_env}")
    
    # Frontend .env
    frontend_env = "frontend/.env"
    if not os.path.exists(frontend_env):
        with open(frontend_env, 'w') as f:
            f.write("VITE_API_URL=http://localhost:5000\n")
        print(f"✅ Created {frontend_env}")

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "=" * 60)
    print("🎉 Setup completed successfully!")
    print("=" * 60)
    print()
    print("📋 Next Steps:")
    print()
    print("1. 🔑 Add your API keys to backend/.env:")
    print("   - GROQ_API_KEY=your_groq_api_key_here")
    print("   - VIDEODB_API_KEY=your_videodb_api_key_here")
    print("   - HUGGINGFACE_API_KEY=your_huggingface_api_key_here")
    print()
    print("2. 🚀 Start the application:")
    print("   Backend:  python backend/app.py")
    print("   Frontend: cd frontend && npm run dev")
    print()
    print("3. 🌐 Open your browser:")
    print("   Frontend: http://localhost:8080")
    print("   Backend API: http://localhost:5000")
    print()
    print("4. 📹 Upload a video and test the content moderation!")
    print()
    print("💡 For the FutureSmart AI Challenge:")
    print("   - Showcase real-time content moderation")
    print("   - Demonstrate AI model integration")
    print("   - Highlight business value and use cases")
    print()

def main():
    """Main setup function"""
    print_banner()
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_node_version():
        return False
    
    # Setup components
    if not setup_backend():
        print("❌ Backend setup failed")
        return False
    
    if not setup_frontend():
        print("❌ Frontend setup failed")
        return False
    
    # Create environment files
    create_env_files()
    
    # Print next steps
    print_next_steps()
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
