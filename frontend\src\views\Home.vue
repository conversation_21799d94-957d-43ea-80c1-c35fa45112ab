<template>
  <div class="home-container">
    <!-- Hero Section -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          <el-icon class="hero-icon"><VideoCamera /></el-icon>
          AI Content Moderator
        </h1>
        <p class="hero-subtitle">
          Intelligent video content moderation using cutting-edge AI models
        </p>
        <p class="hero-description">
          Built for the <strong>FutureSmart AI Challenge</strong> using Groq Whisper, 
          open-source AI models, and VideoDB infrastructure.
        </p>
        
        <div class="hero-actions">
          <el-button type="primary" size="large" @click="$router.push('/upload')">
            <el-icon><Upload /></el-icon>
            Start Moderating Content
          </el-button>
          <el-button size="large" @click="scrollToFeatures">
            <el-icon><InfoFilled /></el-icon>
            Learn More
          </el-button>
        </div>
      </div>
      
      <div class="hero-visual">
        <div class="floating-card">
          <el-icon class="card-icon"><Shield /></el-icon>
          <h3>AI-Powered</h3>
          <p>Advanced machine learning models</p>
        </div>
        <div class="floating-card">
          <el-icon class="card-icon"><Lightning /></el-icon>
          <h3>Real-time</h3>
          <p>Instant content analysis</p>
        </div>
        <div class="floating-card">
          <el-icon class="card-icon"><Check /></el-icon>
          <h3>Accurate</h3>
          <p>High-precision detection</p>
        </div>
      </div>
    </div>

    <!-- Features Section -->
    <div class="features-section" ref="featuresSection">
      <div class="section-header">
        <h2>Powerful Features</h2>
        <p>Comprehensive content moderation capabilities</p>
      </div>
      
      <div class="features-grid">
        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon class="feature-icon" color="#e74c3c"><Warning /></el-icon>
              <h3>NSFW Detection</h3>
            </div>
          </template>
          <p>Automatically detect and flag inappropriate visual content with high accuracy using state-of-the-art computer vision models.</p>
          <ul>
            <li>Real-time frame analysis</li>
            <li>Configurable sensitivity thresholds</li>
            <li>Detailed confidence scores</li>
          </ul>
        </el-card>

        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon class="feature-icon" color="#f39c12"><Microphone /></el-icon>
              <h3>Audio Analysis</h3>
            </div>
          </template>
          <p>Transcribe and analyze audio content using Groq Whisper for speech-to-text and toxicity detection.</p>
          <ul>
            <li>Groq Whisper integration</li>
            <li>Hate speech detection</li>
            <li>Profanity filtering</li>
          </ul>
        </el-card>

        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon class="feature-icon" color="#9b59b6"><TrendCharts /></el-icon>
              <h3>Violence Detection</h3>
            </div>
          </template>
          <p>Identify violent content and aggressive behavior in video frames using advanced image classification.</p>
          <ul>
            <li>Weapon detection</li>
            <li>Aggressive behavior analysis</li>
            <li>Context-aware classification</li>
          </ul>
        </el-card>

        <el-card class="feature-card" shadow="hover">
          <template #header>
            <div class="feature-header">
              <el-icon class="feature-icon" color="#27ae60"><DocumentChecked /></el-icon>
              <h3>Detailed Reports</h3>
            </div>
          </template>
          <p>Generate comprehensive moderation reports with actionable insights and recommendations.</p>
          <ul>
            <li>Risk scoring system</li>
            <li>Timestamp-based findings</li>
            <li>Actionable recommendations</li>
          </ul>
        </el-card>
      </div>
    </div>

    <!-- Technology Stack -->
    <div class="tech-section">
      <div class="section-header">
        <h2>Technology Stack</h2>
        <p>Built with cutting-edge open-source technologies</p>
      </div>
      
      <div class="tech-grid">
        <div class="tech-item">
          <el-icon class="tech-icon"><Cpu /></el-icon>
          <h4>Groq Whisper</h4>
          <p>Ultra-fast speech-to-text transcription</p>
        </div>
        <div class="tech-item">
          <el-icon class="tech-icon"><Monitor /></el-icon>
          <h4>Transformers</h4>
          <p>State-of-the-art NLP and computer vision models</p>
        </div>
        <div class="tech-item">
          <el-icon class="tech-icon"><VideoCamera /></el-icon>
          <h4>VideoDB</h4>
          <p>Video-as-data infrastructure platform</p>
        </div>
        <div class="tech-item">
          <el-icon class="tech-icon"><Connection /></el-icon>
          <h4>PyTorch</h4>
          <p>Deep learning framework for AI models</p>
        </div>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="cta-section">
      <div class="cta-content">
        <h2>Ready to Moderate Your Content?</h2>
        <p>Upload a video and see our AI content moderator in action</p>
        <el-button type="primary" size="large" @click="$router.push('/upload')">
          <el-icon><Upload /></el-icon>
          Upload Video Now
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'Home',
  setup() {
    const featuresSection = ref(null)

    const scrollToFeatures = () => {
      featuresSection.value?.scrollIntoView({ behavior: 'smooth' })
    }

    return {
      featuresSection,
      scrollToFeatures
    }
  }
}
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  min-height: 500px;
  margin-bottom: 80px;
}

.hero-content {
  text-align: left;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.hero-icon {
  color: #667eea;
  font-size: 3.5rem;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #7f8c8d;
  margin-bottom: 15px;
}

.hero-description {
  font-size: 1.1rem;
  color: #5a6c7d;
  margin-bottom: 30px;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.hero-visual {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
}

.floating-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  transform: translateX(20px);
  animation: float 6s ease-in-out infinite;
}

.floating-card:nth-child(2) {
  animation-delay: -2s;
  transform: translateX(-20px);
}

.floating-card:nth-child(3) {
  animation-delay: -4s;
  transform: translateX(20px);
}

.card-icon {
  font-size: 2.5rem;
  color: #667eea;
  margin-bottom: 10px;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) translateX(20px); }
  50% { transform: translateY(-20px) translateX(20px); }
}

/* Features Section */
.features-section {
  margin-bottom: 80px;
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 15px;
}

.section-header p {
  font-size: 1.2rem;
  color: #7f8c8d;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.feature-card {
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.feature-icon {
  font-size: 2rem;
}

.feature-card h3 {
  margin: 0;
  color: #2c3e50;
}

.feature-card p {
  color: #5a6c7d;
  line-height: 1.6;
  margin-bottom: 15px;
}

.feature-card ul {
  list-style: none;
  padding: 0;
}

.feature-card li {
  color: #7f8c8d;
  margin-bottom: 8px;
  position: relative;
  padding-left: 20px;
}

.feature-card li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #27ae60;
  font-weight: bold;
}

/* Technology Section */
.tech-section {
  margin-bottom: 80px;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.tech-item {
  text-align: center;
  padding: 30px 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.tech-item:hover {
  transform: translateY(-5px);
}

.tech-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 15px;
}

.tech-item h4 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.tech-item p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 60px 40px;
  border-radius: 20px;
  margin-bottom: 40px;
}

.cta-content h2 {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 40px;
  }
  
  .hero-title {
    font-size: 2.5rem;
    justify-content: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .tech-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .cta-section {
    padding: 40px 20px;
  }
  
  .cta-content h2 {
    font-size: 2rem;
  }
}
</style>
