import os
from transformers import pipeline
from typing import List
from dotenv import load_dotenv

load_dotenv()

# Load LLM from Hugging Face
# You can use: mistralai/Mixtral, meta-llama, phi-3, etc.
model_name = os.getenv("QUIZ_MODEL", "tiiuae/falcon-7b-instruct")

quiz_llm = pipeline("text-generation", model=model_name, max_new_tokens=256)

# 🧠 Prompt template
def generate_quiz_prompt(summary: str, difficulty: str = "medium") -> str:
    return (
        f"Generate 3 multiple-choice questions with 4 options each based on the following content. "
        f"Set difficulty level to {difficulty}.\n\n"
        f"Content:\n{summary}\n\n"
        f"Format:\nQ: ...\nA. ...\nB. ...\nC. ...\nD. ...\nAnswer: ..."
    )

# 🧪 Main logic
def generate_quiz(summary_text: str, difficulty: str = "medium") -> List[str]:
    prompt = generate_quiz_prompt(summary_text, difficulty)
    response = quiz_llm(prompt)[0]["generated_text"]
    questions = response.strip().split("\n\n")
    return questions
