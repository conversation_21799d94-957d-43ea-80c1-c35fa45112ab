# API Keys
GROQ_API_KEY=your_groq_api_key_here
VIDEODB_API_KEY=your_videodb_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# Optional API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Application Configuration
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_PORT=5000

# Database Configuration
DATABASE_URL=sqlite:///content_moderator.db

# File Upload Configuration
MAX_FILE_SIZE=500MB
UPLOAD_FOLDER=uploads
ALLOWED_EXTENSIONS=mp4,avi,mov,mkv,webm,m4v

# AI Model Configuration
DEVICE=auto
MODEL_CACHE_DIR=./models
BATCH_SIZE=8

# Content Moderation Thresholds
NSFW_THRESHOLD=0.8
VIOLENCE_THRESHOLD=0.7
HATE_SPEECH_THRESHOLD=0.6
PROFANITY_THRESHOLD=0.5
COPYRIGHT_THRESHOLD=0.9

# Slack Integration (Optional)
SLACK_BOT_TOKEN=your_slack_bot_token_here
SLACK_CHANNEL=content-moderation-alerts

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/content_moderator.log

# Security
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
